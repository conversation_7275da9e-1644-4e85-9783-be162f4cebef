# Production Environment Configuration
# Copy this file to .env and update with your actual values

# Application
NODE_ENV=production
PORT=5001

# Database
MONGODB_URI=mongodb://localhost:27017/ai-blog-platform-prod

# AI Services - Vertex AI (Primary)
GOOGLE_CLOUD_PROJECT=your_google_cloud_project_id
VERTEX_AI_PROJECT=your_google_cloud_project_id
VERTEX_AI_PROJECT_ID=your_google_cloud_project_id
GOOGLE_CLOUD_LOCATION=us-central1
VERTEX_AI_LOCATION=us-central1
VERTEX_AI_MODEL_NAME=gemini-2.0-flash-exp
GOOGLE_APPLICATION_CREDENTIALS=/app/service_account_key.json

# AI Services - Fallback
GEMINI_API_KEY=your_gemini_api_key_here

# WordPress Integration - WattMonk
WATTMONK_WORDPRESS_BASE_URL=https://www.wattmonk.com
WATTMONK_WORDPRESS_USERNAME=your_wattmonk_wp_username
WATTMONK_WORDPRESS_APP_PASSWORD=your_wattmonk_wp_app_password

# WordPress Integration - Ensite
ENSITE_WORDPRESS_BASE_URL=https://ensite-website.com
ENSITE_WORDPRESS_USERNAME=your_ensite_wp_username
ENSITE_WORDPRESS_APP_PASSWORD=your_ensite_wp_app_password

# External APIs - SERP & Research
SERP_API_KEY=your_serp_api_key
SERPER_API_KEY=your_serper_api_key
PERPLEXITY_API_KEY=your_perplexity_api_key
RAPIDAPI_KEY=your_rapidapi_key

# News & Trends APIs
GNEWS_API_KEY=your_gnews_api_key
NEWSDATA_API_KEY=your_newsdata_api_key

# Google Sheets Integration
GOOGLE_SHEETS_API_KEY=your_google_sheets_api_key
BLOG_DATA_SPREADSHEET_ID=your_spreadsheet_id
COMPANY_DATA_SPREADSHEET_ID=your_company_spreadsheet_id

# AWS S3 (for image storage)
AWS_ACCESS_KEY_ID=your_aws_access_key
AWS_SECRET_ACCESS_KEY=your_aws_secret_key
AWS_REGION=ap-south-1
AWS_S3_BUCKET=aibucketwattmonk

# Security
JWT_SECRET=your_jwt_secret_here
BCRYPT_ROUNDS=12

# CORS
FRONTEND_URL=http://localhost:3001

# Logging
LOG_LEVEL=info
LOG_FILE=logs/app.log

# Rate Limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# N8N Integration (if using)
N8N_WEBHOOK_URL=https://your-n8n-instance.com/webhook/wordpress
N8N_API_KEY=your_n8n_api_key
