node_modules
npm-debug.log
.git
.gitignore
README.md
.env
.env.example
.env.production
.nyc_output
coverage
.nyc_output
.vscode
.DS_Store
logs
*.log
Dockerfile
.dockerignore

# Test and debug files
test-*.js
debug-*.js
debug-*.html
clean-*.html
*-sample.html
check-env.js
create-clean-content.js

# Alternative servers (not needed in production)
minimal-server.js
simple-server.js
test-server.js

# System files
bash.exe.stackdump
*.stackdump

# Development files
VERTEX_AI_SETUP.md
DOCKER_DEPLOYMENT.md
